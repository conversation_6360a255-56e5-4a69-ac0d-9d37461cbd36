import BExtendedInfo from 'model/remote/price/controller/quotationapply/other/BExtendedInfo'
import BYearGuidePriceMatchResult from 'model/remote/price/controller/yearguideprice/yearguideprice/BYearGuidePriceMatchResult'
import { DataSource } from 'model/remote/price/model/po/DataSource'

// 报价总览-填写折扣率物料明细
export default class BQuotationApplyPriceDiscountMatLine {
  // 是否反算，仅用于区分正算反算，不保存到数据库
  backwardCalc: Nullable<boolean> = null
  // ID
  id: Nullable<string> = null
  // 数据来源
  dataSource: Nullable<DataSource> = null
  // 所属报价申请单, QuotationApplyBill.id
  owner: Nullable<string> = null
  // 行号
  line: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 指导价
  exwPrice: Nullable<number> = null
  // 是否使用特价
  useSpecial: Nullable<boolean> = null
  // 台量
  qty: Nullable<number> = null
  // 额外费用用途
  extraFeeUsage: Nullable<string> = null
  // 发货地/出发地代码
  domesticFreightLandCode: Nullable<string> = null
  // 发货地/出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortName: Nullable<string> = null
  // 延保政策
  extendedInfos: BExtendedInfo[] = []
  // 折扣率
  discountRate: Nullable<number> = null
  // 设备报价/台
  actualPrice: Nullable<number> = null
  // 综合报价/台
  quotationTotal: Nullable<number> = null
  // 国内段运杂费
  domesticFreight: Nullable<number> = null
  // 国际/境外运费
  internationalFreight: Nullable<number> = null
  // 其他费用
  otherFreight: Nullable<number> = null
  // 设备总报价（物料行）
  actualPriceLineTotal: Nullable<number> = null
  // 综合总报价（物料行）
  quotationTotalLineTotal: Nullable<number> = null
  // 国内段总运杂费（物料行）
  domesticFreightLineTotal: Nullable<number> = null
  // 国际/境外总运费（物料行）
  internationalFreightLineTotal: Nullable<number> = null
  // 其他总费用（物料行）
  otherFreightLineTotal: Nullable<number> = null
  // 年度指导价计算结果
  guidePrice: Nullable<BYearGuidePriceMatchResult> = null
}