import ApiClient from 'http/ApiClient'
import ApplicationScopeExpenseRate from 'model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRate'
import ApplicationScopeExpenseRateCalRequest from 'model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRateCalRequest'
import LtcResponse from 'model/remote/support/core/domain/LtcResponse'
import SpecialApplyApplicationScopePayType from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyApplicationScopePayType'
import SpecialApplyCtryRate from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyCtryRate'
import SpecialApplyInterTransPremiumRuleSaveRequest from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyInterTransPremiumRuleSaveRequest'
import SpecialApplyOtherExpensesMatLineSaveRequest from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyOtherExpensesMatLineSaveRequest'
import SpecialApplyOtherExpensesSummaryResponse from 'model/remote/price/controller/specialapply/otherexpenses/SpecialApplyOtherExpensesSummaryResponse'
import SpecialApplyPayTypeSaveRequest from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyPayTypeSaveRequest'
import { InterTransPremiumRuleType } from 'model/remote/price/api/quotationapply/bill/InterTransPremiumRuleType'

export default class SpecialApplyBillOtherExpenesApi {
  /**
   * 远期费率计算，包含信保费与远期贴现费
   * 
   */
  static calRate(body: ApplicationScopeExpenseRateCalRequest): Promise<LtcResponse<ApplicationScopeExpenseRate[]>> {
    return ApiClient.server().post(`/price/specialApply/bill/otherExpenes/calRate`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 计算国际运输保险费费率
   * 
   * @param type 舱内舱外类型
   */
  static calcInterTransPremiumRule(billId: string, type: InterTransPremiumRuleType): Promise<LtcResponse<SpecialApplyCtryRate[]>> {
    return ApiClient.server().post(`/price/specialApply/bill/otherExpenes/interTransPremiumRule/calc`, {}, {
      params: {
        billId: billId,
        type: type
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取其他费用汇总信息
   * 
   */
  static getExpensesSummary(billId: string): Promise<LtcResponse<SpecialApplyOtherExpensesSummaryResponse>> {
    return ApiClient.server().get(`/price/specialApply/bill/otherExpenes/expensesSummary/getBillId`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取国际运输保险费费率
   * 
   */
  static getInterTransPremiumRule(billId: string): Promise<LtcResponse<SpecialApplyInterTransPremiumRuleSaveRequest>> {
    return ApiClient.server().post(`/price/specialApply/bill/otherExpenes/interTransPremiumRule/getBillId`, {}, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取物料费用明细列表
   * 
   */
  static getMatExpenes(billId: string): Promise<LtcResponse<SpecialApplyOtherExpensesMatLineSaveRequest>> {
    return ApiClient.server().get(`/price/specialApply/bill/otherExpenes/matExpenes/getBillId`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 获取支付方式
   * 
   */
  static getPayType(billId: string): Promise<LtcResponse<SpecialApplyApplicationScopePayType[]>> {
    return ApiClient.server().get(`/price/specialApply/bill/otherExpenes/payType/getBillId`, {
      params: {
        billId: billId
      }
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存国际运输保险费费率
   * 
   */
  static saveInterTransPremiumRule(body: SpecialApplyInterTransPremiumRuleSaveRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/otherExpenes/interTransPremiumRule/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存物料费用明细列表
   * 
   */
  static saveMatExpenes(body: SpecialApplyOtherExpensesMatLineSaveRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/otherExpenes/mat/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

  /**
   * 保存支付方式明细列表
   * 
   */
  static savePayType(body: SpecialApplyPayTypeSaveRequest): Promise<LtcResponse<void>> {
    return ApiClient.server().post(`/price/specialApply/bill/otherExpenes/payType/save`, body, {
    }).then((res) => {
      return res.data
    })
  }

}
