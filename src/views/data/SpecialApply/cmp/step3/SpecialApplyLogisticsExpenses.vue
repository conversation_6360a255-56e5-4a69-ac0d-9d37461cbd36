<template>
  <div class="special-apply-logistics-expenses">
    <!-- 提示信息 -->
    <div class="tab-top">
      <span
        >系统将按照特价申请提交日期当日全量发运，本单所有【特价适用范围】均同一目的港/目的站、运输模式预估物流费用。</span
      >
    </div>

    <!-- 运输参数配置 -->
    <div class="transportation">
      <el-form size="mini" ref="form" :model="entity" :rules="rules" class="form-row">
        <!-- 自营场景 -->
        <template v-if="baseEntity.exportTypeId === ExportType.self">
          <el-form-item
            v-if="activeSubTab === 1"
            label="目的港/目的站"
            required
            prop="destinationPortCode"
          >
            <SearchPort v-model="destinationPort" @input="destinationPortCodeChange" />
          </el-form-item>
          <el-form-item v-else label="目的港/目的站">
            <span class="form-text">{{ destinationPort.name }}</span>
          </el-form-item>

          <!-- 国际运输模式 -->
          <el-form-item
            v-if="activeSubTab === 1"
            label="国际运输模式"
            prop="transportTypeId"
            required
          >
            <div style="display: flex; align-items: center; gap: 8px;">
              <search-remote
                v-model="entity.transportTypeId"
                placeholder="输入搜索或选择"
                valueKey="dictValue"
                :queryMethod="queryTransportList"
                :initMethod="queryTransportInit"
                :valueFormat="transportValueFormat"
                :labelFormat="transportLabelFormat"
                :multiple="false"
                :lazyLoad="true"
                style="width: 200px"
                @change="transportTypeIdChange"
              />
              <!-- 二级下拉框：当选择"海运滚装散杂"时显示 -->
              <el-form-item
                v-if="entity.transportTypeId === 'oceanRoll'"
                prop="transportTypeRoleId"
                style="margin-bottom: 0;"
              >
                <el-select
                  v-model="entity.transportTypeRoleId"
                  placeholder="请选择"
                  style="width: 120px"
                  @change="transportTypeRoleIdChange"
                >
                  <el-option :label="'滚装'" :value="oceanRollRollLabel" />
                  <el-option :label="'散杂'" :value="oceanRollScatterLabel" />
                </el-select>
              </el-form-item>
            </div>
          </el-form-item>

          <el-form-item v-else label="国际运输模式">
            <span class="form-text">{{ transportTypeName || entity.transportTypeId }}</span>
          </el-form-item>
        </template>

        <!-- 供货场景 -->
        <template v-else>
          <!-- 交货地点 -->
          <el-form-item label="交货地点" prop="domesticFreightLandCode" required>
            <search-remote
              v-model="entity.domesticFreightLandCode"
              placeholder="输入搜索或选择"
              valueKey="code"
              :queryMethod="queryFreightPort"
              :valueFormat="freightPortValueFormat"
              :labelFormat="freightPortLabelFormat"
              :initMethod="queryFreightPortInit"
              :multiple="false"
              :lazyLoad="true"
              style="width: 200px"
              @change="handleFreightPort"
            />
          </el-form-item>

          <!-- 国内运输模式 -->
          <el-form-item label="国内运输模式">
            <span class="form-text">汽车</span>
          </el-form-item>
        </template>
      </el-form>
    </div>

    <!-- 子步骤导航和内容 -->
    <div class="step-tabs">
      <div class="step-tabs-header">
        <div
          v-for="item in tabList"
          :key="item.step"
          class="step-tab-item"
          :class="{ active: activeSubTab === item.step }"
          @click="handleStepTabClick(item.step)"
        >
          <div class="step-number">{{ item.step }}</div>
          <div class="step-title">{{ item.title }}</div>
        </div>
      </div>

      <!-- 子步骤内容 -->
      <div class="step-content">
        <!-- 子步骤1：物料信息 -->
        <LogisticsExpensesStep1
          v-if="activeSubTab === 1"
          :key="`logistics-step1-${baseEntity.id}`"
          ref="logisticsStep1"
          :baseEntity="baseEntity"
          :entity="entity"
          @refresh="refreshData"
        />

        <!-- 子步骤2：装运方案 -->
        <LogisticsExpensesStep2
          v-if="activeSubTab === 2"
          :key="`logistics-step2-${baseEntity.id}`"
          ref="logisticsStep2"
          :baseEntity="baseEntity"
          :entity="entity"
          @refresh="refreshData"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./SpecialApplyLogisticsExpenses.ts"></script>

<style lang="scss" scoped>
.special-apply-logistics-expenses {
  .tab-top {
    font-size: 12px;
    color: #242633;
    display: flex;
    flex-direction: column;
  }

  .transportation {
    margin-top: 8px;
    margin-bottom: 8px;

    .form-row {
      display: flex;
      gap: 24px;
    }
  }

  .step-tabs {
    flex: 1;
    display: flex;
    border-radius: 4px;
    border: 1px solid #d7dfeb;
    margin-bottom: 12px;

    .step-tabs-header {
      flex-shrink: 0;
      flex-grow: 0;
      width: 160px;
      color: #79879e;
      background-color: #f7f9fc;

      .step-tab-item {
        display: flex;
        align-items: center;
        height: 56px;
        padding: 0px 12px;
        cursor: pointer;

        .step-number {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border-radius: 10px;
          background-color: #e5e8eb;
          margin-right: 12px;
        }

        .step-title {
          font-size: 14px;
        }

        &.active {
          background-color: #fff;

          .step-number {
            font-weight: bold;
            color: #fff;
            background-color: $--color-primary;
          }

          .step-title {
            font-weight: bold;
            color: $--color-primary;
          }
        }
      }
    }
  }

  .step-content {
    width: 0px;
    flex-grow: 1;
    flex-shrink: 1;
    padding: 12px;
  }
}

.form-text {
  color: #606266;
  font-size: 12px;
}
</style>
