<template>
  <el-dialog
    title="修改国际运输保险费"
    width="40%"
    @close="handleCancel"
    :visible="true"
    :close-on-click-modal="false"
  >
    <el-form class="form" ref="form" size="mini" :model="form">
      <div class="form-title">一切险</div>
      <div class="form-content">
        <span>合同单价 * </span>
        <number-input
          size="mini"
          style="width: 56px;"
          :min="1.1"
          :max="1.3"
          v-model="form.advanceRate"
          xType="float"
        >
        </number-input>
        <span>
          * {{ displayRateText }}</span
        >
        <el-button
          size="mini"
          type="text"
          @click="showRateDetail"
          style="margin-left: 8px;"
        >
          查看明细
        </el-button>
        <el-radio-group
          v-if="showType"
          class="form-content-radio"
          v-model="form.type"
          @change="typeChange"
        >
          <el-radio label="out">舱外</el-radio>
          <el-radio label="in">舱内</el-radio>
        </el-radio-group>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleCancel">取消</el-button>
      <el-button size="mini" type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script src="./SettingFee.ts"></script>

<style lang="scss" scoped>
/deep/ .el-dialog__header {
  padding: 10px 20px;
  font-size: 14px;
  color: #242633;
  line-height: 22px;
}
/deep/ .el-dialog__body {
  padding: 16px 24px;
}
/deep/ .el-table__cell {
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .cell {
  padding: 0px 12px;
}
.form {
  font-size: 12px;
  &-title {
    font-weight: bold;
    color: #242633;
    margin-bottom: 4px;
  }
  &-content {
    margin-bottom: 4px;
    &-radio {
      margin-left: 12px;
      /deep/ .el-radio {
        margin-right: 4px;
        .el-radio__label {
          padding-left: 4px;
        }
      }
    }
  }
}
</style>
