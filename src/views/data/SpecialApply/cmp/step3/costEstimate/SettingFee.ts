import { Vue, Component } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
// MODELS
import SpecialApplyInterTransPremiumRule from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyInterTransPremiumRule';
// COMPONENTES
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import Dialog from '@/components/dialog/Dialog';
import RateDetailDialog from './RateDetailDialog.vue';

@Component({
  name: 'SettingFee',
  components: { NumberInput },
})
export default class SettingFee extends Vue {
  ctryId; // 国家
  transportTypeId; // 国际运输模式
  entity;
  success; // 成功回调
  $refs: any;

  form: SpecialApplyInterTransPremiumRule = new SpecialApplyInterTransPremiumRule();
  PrecisionUtil = PrecisionUtil;

  // 是否展示舱内舱外
  get showType() {
    // 国际运输模式为海运滚装散杂时显示舱内舱外选择
    return this.transportTypeId === 'oceanRoll';
  }

  // 获取用于显示的费率
  get displayRate() {
    if (this.form.ctryRate && this.form.ctryRate.length > 0) {
      const validRate = this.form.ctryRate.find((item) => item.rate !== null);
      return validRate ? validRate.rate : 0;
    }
    return 0;
  }

  created() {
    this.form = this.entity;
  }
  // 切换舱内舱外
  typeChange(value: any) {
    this.form.type = value;
  }

  // 显示费率明细
  showRateDetail() {
    new Dialog(RateDetailDialog, {
      ctryRate: this.form.ctryRate || [],
    }).show();
  }

  handleCancel() {
    this.$emit('hide');
  }

  handleSubmit() {
    if (!this.form.advanceRate) {
      return this.$message.error('加价系数不能为空');
    }
    this.success(this.form);
    this.handleCancel();
  }
}
