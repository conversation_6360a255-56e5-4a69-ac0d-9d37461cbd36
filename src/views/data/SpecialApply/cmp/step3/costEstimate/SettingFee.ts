import { Vue, Component } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
// MODELS
import SpecialApplyInterTransPremiumRule from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyInterTransPremiumRule';
// COMPONENTES
import NumberInput from '@/components/form/number-input/NumberInput.vue';

@Component({
  name: 'SettingFee',
  components: { NumberInput },
})
export default class SettingFee extends Vue {
  ctryId: any; // 国家
  transportTypeId: any; // 国际运输模式
  entity: any;
  success: any; // 成功回调
  $refs: any;

  form: SpecialApplyInterTransPremiumRule = new SpecialApplyInterTransPremiumRule();
  PrecisionUtil = PrecisionUtil;

  // 费率明细弹窗相关
  rateDetailVisible: boolean = false;

  // 是否展示舱内舱外
  get showType() {
    // 国际运输模式为海运滚装散杂时显示舱内舱外选择
    return this.transportTypeId === 'oceanRoll';
  }

  // 获取用于显示的费率
  get displayRate() {
    if (this.form.ctryRate && this.form.ctryRate.length > 0) {
      const validRate = this.form.ctryRate.find((item) => item.rate !== null);
      return validRate ? validRate.rate : 0;
    }
    return 0;
  }

  // 获取用于显示的费率文本
  get displayRateText() {
    const rate = this.displayRate;
    if (rate && rate > 0) {
      const percentage = PrecisionUtil.floatMul(rate, 100);
      return `${percentage.toFixed(4)}%`;
    }
    return '其他默认费率';
  }

  created() {
    this.form = this.entity;
  }

  // 切换舱内舱外
  typeChange(value: any) {
    this.form.type = value;
  }

  // 显示费率明细
  showRateDetail() {
    this.rateDetailVisible = true;
    this.loadRateDetail();
  }

  // 加载费率明细数据
  loadRateDetail() {
    if (!this.baseEntity?.id) {
      this.$message.error('未获取到申请单信息');
      return;
    }

    this.rateDetailLoading = true;
    this.rateDetailData = [];

    // 获取申请单基本信息，包含销往国
    SpecialApplyBillApi.get(this.baseEntity.id)
      .then((res) => {
        if (res.data) {
          const bill = res.data;
          const countries: any[] = [];

          // 提取所有销往国
          if (bill.applicationScope && bill.applicationScope.length > 0) {
            bill.applicationScope.forEach((scope: any) => {
              if (scope.ctrys && scope.ctrys.length > 0) {
                scope.ctrys.forEach((ctryGroup: any) => {
                  if (ctryGroup.applicationScopeCtry && ctryGroup.applicationScopeCtry.length > 0) {
                    ctryGroup.applicationScopeCtry.forEach((ctry: any) => {
                      if (ctry.id && ctry.name) {
                        // 避免重复添加相同国家
                        const exists = countries.find((c) => c.id === ctry.id);
                        if (!exists) {
                          countries.push({
                            id: ctry.id,
                            name: ctry.name,
                            code: ctry.code,
                          });
                        }
                      }
                    });
                  }
                });
              }
            });
          }

          // 如果没有销往国，显示提示
          if (countries.length === 0) {
            this.rateDetailData = [
              {
                ctryName: '暂无销往国信息',
                rate: null,
              },
            ];
            this.rateDetailLoading = false;
            return;
          }

          // 获取每个国家的费率
          this.getRatesForCountries(countries);
        } else {
          this.$message.error('获取申请单信息失败');
          this.rateDetailLoading = false;
        }
      })
      .catch((error) => {
        this.$message.error(error.message || '获取申请单信息失败');
        this.rateDetailLoading = false;
      });
  }

  // 获取国家对应的费率
  getRatesForCountries(countries: any[]) {
    const ratePromises = countries.map((country) => {
      const listIn = new InternationalTransportInsuranceRateListIn();
      listIn.countryIdIdEquals = parseInt(country.id);

      return InternationalTransportInsuranceRateApi.listQuery(listIn)
        .then((res) => {
          if (res.data && res.data.length > 0) {
            const rateData = res.data[0];
            // 根据运输方式获取对应费率
            let rate: number | null = null;
            if (rateData.rates && rateData.rates.length > 0) {
              // 这里可以根据运输方式匹配具体费率，暂时取第一个
              rate = rateData.rates[0].rate;
            }
            return {
              ctryName: country.name,
              rate: rate,
            };
          } else {
            return {
              ctryName: country.name,
              rate: null,
            };
          }
        })
        .catch(() => {
          return {
            ctryName: country.name,
            rate: null,
          };
        });
    });

    Promise.all(ratePromises)
      .then((results) => {
        this.rateDetailData = results;
        this.rateDetailLoading = false;
      })
      .catch(() => {
        this.$message.error('获取费率信息失败');
        this.rateDetailLoading = false;
      });
  }

  handleCancel() {
    this.$emit('hide');
  }

  handleSubmit() {
    if (!this.form.advanceRate) {
      return this.$message.error('加价系数不能为空');
    }
    this.success(this.form);
    this.handleCancel();
  }
}
