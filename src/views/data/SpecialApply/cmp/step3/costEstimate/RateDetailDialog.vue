<template>
  <el-dialog
    title="查看国际运输保险费率"
    :visible.sync="dialogVisible"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-table :data="ctryRateData" size="mini" :border="false">
      <el-table-column prop="ctry.name" label="销往国" min-width="150">
        <template slot-scope="{ row }">
          {{ (row.ctry && row.ctry.name) || '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="rate" label="国际运输保险费率" align="left" min-width="150">
        <template slot-scope="{ row }">
          <span v-if="row.rate !== null && row.rate !== undefined">
            {{ PrecisionUtil.floatMul(row.rate, 100) | numberFilter('--', 2, false) }}%
          </span>
          <span v-else style="color: #F56C6C;">未获取到</span>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script src="./RateDetailDialog.ts"></script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>
