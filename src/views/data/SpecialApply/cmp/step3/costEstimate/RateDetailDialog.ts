import { Vue, Component } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';

@Component({
  name: 'RateDetailDialog',
})
export default class RateDetailDialog extends Vue {
  ctryRate: any; // 从父组件传入的费率数据
  ctryRateData: any[] = [];
  dialogVisible: boolean = true;
  PrecisionUtil = PrecisionUtil;

  created() {
    // 从父组件传入的数据
    this.ctryRateData = this.ctryRate || [];
  }

  handleClose() {
    this.$emit('hide');
  }
}
