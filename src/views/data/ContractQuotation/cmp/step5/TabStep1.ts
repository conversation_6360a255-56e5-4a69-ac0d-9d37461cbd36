/*
 * @Author: 张文轩
 * @Date: 2024-06-27 17:22:06
 * @LastEditTime: 2024-09-27 19:35:14
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step5\TabStep1.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import { RouterNames } from '@/router/modules';
import ObjectUtil from '@/utils/ObjectUtil';
import PrecisionUtil from '@/utils/Precision';
// HTTPS
import QuotationApplyPriceApi from '@/http/price/controller/quotationapply/price/QuotationApplyPriceApi';
// MODELS
import BQuotationApplyBill from '@/model/remote/price/controller/quotationapply/bill/BQuotationApplyBill';
import BQuotationApplyDiscountRequest from '@/model/remote/price/controller/quotationapply/price/BQuotationApplyDiscountRequest';
// COMPONENTES
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import Dialog from '@/components/dialog/Dialog';
import SubmitDialog from '../drawer/SubmitDialog.vue';
@Component({
  name: 'TabStep1',
  components: { SearchRemote, NumberInput },
})
export default class TabStep1 extends Vue {
  @Prop({ type: Object, default: new BQuotationApplyBill() })
  baseEntity: BQuotationApplyBill; // 基础数据
  ObjectUtil = ObjectUtil;
  PrecisionUtil = PrecisionUtil;
  entity: BQuotationApplyDiscountRequest = new BQuotationApplyDiscountRequest();
  tableData: any[] = [];
  tableSpan: any = {};
  loading: boolean = false;
  height: string = '400px';
  get otherDisabled() {
    if (
      !this.baseEntity.state ||
      this.baseEntity.state === 'draft' ||
      ['commerceReview', 'subsidiaryCeoReturn', 'priceSpecialistReturn'].includes(
        this.baseEntity.taskStateCode!
      )
    ) {
      return false;
    }
    return true;
  }
  get other1Disabled() {
    if (
      !this.baseEntity.state ||
      this.baseEntity.state === 'draft' ||
      [
        'commerceReview',
        'subsidiaryCeoReturn',
        'priceSpecialistReturn',
        'commerceConfirm',
        'priceManagementCommitteeReturn',
      ].includes(this.baseEntity.taskStateCode!)
    ) {
      return false;
    }
    return true;
  }
  created() {
    // 查询物料折扣率
    this.loading = true;
    QuotationApplyPriceApi.getMatDiscount(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.entity = res.data;
          let tableData = res.data.matLines;
          // 计算折扣率是正还是负
          this.tableData = tableData.map((item) => {
            return {
              ...item,
              discountRateZhen:
                item.discountRate && item.discountRate > 0
                  ? PrecisionUtil.floatMul(item.discountRate, 100)
                  : null,
              discountRateFu:
                item.discountRate && item.discountRate < 0
                  ? PrecisionUtil.floatMul(Math.abs(item.discountRate), 100)
                  : null,
            };
          });
          this.getTableSpan(res.data.matLines);
          // 计算汇总信息
          this.getSummaryInfo(this.tableData);
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  mounted() {
    this.resizeHandler();
    window.addEventListener('resize', this.resizeHandler);
  }
  // 获取表格合并数据
  getTableSpan(result) {
    let spanArr: any = {};
    result.forEach((item, index) => {
      if (index === 0) {
        spanArr[index] = 1;
      } else {
        let lastIndex = 0;
        for (let key in spanArr) {
          if (Number(key) > lastIndex) {
            lastIndex = Number(key);
          }
        }
        if (item.i18ProdGroupId === result[lastIndex].i18ProdGroupId) {
          spanArr[lastIndex] += 1;
        } else {
          spanArr[index] = 1;
        }
      }
    });
    this.tableSpan = spanArr;
  }
  // 表格合并
  spanMethod({ row, column, rowIndex, columnIndex }) {
    if (columnIndex === 0) {
      if (this.tableSpan[rowIndex]) {
        return {
          rowspan: this.tableSpan[rowIndex],
          colspan: 1,
        };
      } else {
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    }
  }
  // 计算汇总信息
  getSummaryInfo(result) {
    let exwPrice = 0; // 设备指导价
    let actualPrice = 0; // 设备总报价
    let quotationTotal = 0; // 综合报价
    result.forEach((item, index) => {
      exwPrice += PrecisionUtil.floatMul(item.exwPrice || 0, item.qty);
      actualPrice += PrecisionUtil.floatMul(item.actualPrice || 0, item.qty);
      quotationTotal += PrecisionUtil.floatMul(item.quotationTotal || 0, item.qty);
    });
    // 向上取整
    exwPrice = Math.ceil(exwPrice);
    actualPrice = Math.ceil(actualPrice);
    quotationTotal = Math.ceil(quotationTotal);
    this.$emit('summary', { exwPrice, actualPrice, quotationTotal });
  }
  // 是否允许负折扣
  hasRateFu(row) {
    let hasRateFu = true;
    // 买断不允许负折扣
    if (this.baseEntity.saleMode === 'buyOut') {
      hasRateFu = false;
    } else {
      // 非买断使用特价 且 特价不允许折扣
      if (row.useSpecial === true && !(row.guidePrice && row.guidePrice.discount)) {
        hasRateFu = false;
      }
    }
    return hasRateFu;
  }
  // 折扣率变动
  handleChange(row, index, key) {
    // 正/负折扣率变动清空负/正折扣率
    if (!ObjectUtil.isNullOrBlank(row[key])) {
      row[key] = undefined;
    }
    row.discountRate = null;
    if (!ObjectUtil.isNullOrBlank(row.discountRateFu)) {
      if (Number(row.discountRateFu) > 99.99) {
        row.discountRateFu = 99.99;
      }
      row.discountRate = Number('-' + PrecisionUtil.floatDiv(row.discountRateFu, 100));
    }
    if (!ObjectUtil.isNullOrBlank(row.discountRateZhen)) {
      if (Number(row.discountRateZhen) > 99.99) {
        row.discountRateZhen = 99.99;
      }
      row.discountRate = Number(PrecisionUtil.floatDiv(row.discountRateZhen, 100));
    }
    // 前端自动计算设备报价
    // 设备报价 = 设备指导价*（1+折扣率）
    row.actualPrice = Math.ceil(
      PrecisionUtil.floatMul(PrecisionUtil.floatAdd(row.discountRate, 1), row.exwPrice)
    );
    this.loading = true;
    QuotationApplyPriceApi.calMatOtherFee(row)
      .then((res) => {
        if (res.data) {
          let rowData = { ...row, ...res.data };
          this.$set(this.tableData, index, rowData);
          // 计算汇总信息
          this.getSummaryInfo(this.tableData);
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  // 设备报价变动
  actualPriceChange(row, index) {
    // 前端自动计算调价幅度
    // 折扣率 = 设备报价/设备指导价 - 1
    if (!this.hasRateFu(row) && row.actualPrice < row.exwPrice) {
      // 不允许产生折扣的数据，设备报价不能小于设备指导价
      row.actualPrice = row.exwPrice;
    }
    row.discountRate = PrecisionUtil.toFixed(
      PrecisionUtil.floatSub(PrecisionUtil.floatDiv(row.actualPrice, row.exwPrice), 1),
      4
    );
    if (Number(row.discountRate) >= 0.9999) {
      row.discountRate = 0.9999;
      row.actualPrice = Math.ceil(PrecisionUtil.floatMul(row.exwPrice, 1.9999));
    }
    if (Number(row.discountRate) <= -0.9999) {
      row.discountRate = -0.9999;
      row.actualPrice = Math.ceil(PrecisionUtil.floatMul(row.exwPrice, 0.0001));
    }
    row.discountRateZhen =
      row.discountRate && row.discountRate > 0
        ? PrecisionUtil.floatMul(row.discountRate, 100)
        : null;
    row.discountRateFu =
      row.discountRate && row.discountRate < 0
        ? PrecisionUtil.floatMul(Math.abs(row.discountRate), 100)
        : null;
    this.loading = true;
    QuotationApplyPriceApi.calMatOtherFee(row)
      .then((res) => {
        if (res.data) {
          let rowData = { ...row, ...res.data };
          this.$set(this.tableData, index, rowData);
          // 计算汇总信息
          this.getSummaryInfo(this.tableData);
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  // 是否展示使用特价勾选框
  showUseSpecial(row) {
    let hasSpecialPrice = this.getHasSpecialPrice(row.guidePrice);
    let hasExwPrice = row.guidePrice && row.guidePrice.exwPrice ? true : false;
    // 来源cpq的数据不展示 || 存在指导价不存在
    if (row.dataSource === 'cpq' || (hasExwPrice && !hasSpecialPrice)) {
      return false;
    }
    return true;
  }
  // 切换是否使用特价
  useSpecialChange(row, index, value) {
    let hasSpecialPrice = this.getHasSpecialPrice(row.guidePrice);
    let hasExwPrice = row.guidePrice && row.guidePrice.exwPrice ? true : false;
    // 取消勾选 - 使用普通指导价
    if (value === false) {
      // 无普通指导价
      if (!hasExwPrice) {
        row.useSpecial = true;
        this.$message.error('该物料只有特价，无法切换');
      }
      // 有普通指导价
      else {
        row.useSpecial = false;
        row.exwPrice = row.guidePrice.exwPrice;
        this.useSpecialSuccess(row, index);
      }
    }
    // 勾选 - 使用特价
    else {
      // 有特价
      if (hasSpecialPrice) {
        // 如果存在限量 且 台量大于剩余限量
        if (row.guidePrice.limitMat && row.qty > row.guidePrice.specialPriceMaxQty) {
          row.useSpecial = false;
          this.$message.error(`台量必须大于剩余限量X${row.guidePrice.specialPriceMaxQty}`);
        } else {
          row.useSpecial = true;
          row.exwPrice = row.guidePrice.specialPrice;
          this.useSpecialSuccess(row, index);
        }
      }
      // 无特价 - 该情况不存在不展示勾选框
      else {
      }
    }
  }
  // 成功切换
  useSpecialSuccess(row, index) {
    // 如果为特价且特价不支持负折扣
    if (!this.hasRateFu(row)) {
      if (!ObjectUtil.isNullOrBlank(row.discountRateFu)) {
        row.discountRateFu = null;
        row.discountRate = null;
      }
    }
    // 前端自动计算设备报价
    // 设备报价 = 设备指导价*（1+折扣率）
    row.actualPrice = Math.ceil(
      PrecisionUtil.floatMul(PrecisionUtil.floatAdd(row.discountRate, 1), row.exwPrice)
    );
    this.loading = true;
    QuotationApplyPriceApi.calMatOtherFee(row)
      .then((res) => {
        if (res.data) {
          let rowData = { ...row, ...res.data };
          this.$set(this.tableData, index, rowData);
          // 计算汇总信息
          this.getSummaryInfo(this.tableData);
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  // 获取是否有可用特价
  getHasSpecialPrice(result) {
    if (!result) {
      return false;
    }
    let hasSpecialPrice = false;
    if (result.specialPrice) {
      // 特价限量
      if (result.limitMat) {
        if (result.specialPriceMaxQty && result.specialPriceMaxQty > 0) {
          hasSpecialPrice = true;
        }
      }
      // 特价不限量
      else {
        hasSpecialPrice = true;
      }
    }
    return hasSpecialPrice;
  }
  // 判断该行是否有额外费用用途
  hasExtraFeeUsage(row: any): boolean {
    return !!row.extraFeeUsage;
  }
  // 获取额外费用用途
  getExtraFeeUsage(row: any): string {
    return row.extraFeeUsage || '';
  }
  doSubmit() {
    let params: BQuotationApplyDiscountRequest = this.entity;
    params.matLines = this.tableData;
    new Dialog(SubmitDialog, {
      params: params,
      method: this.submitMethod,
      sucess: () => {
        this.$router.push({
          name: RouterNames.contractQuotationList,
        });
      },
      cancel: () => {
        // @ts-ignore
        this.$parent.goStep1();
      },
    }).show();
  }
  submitMethod(params) {
    return QuotationApplyPriceApi.submitDiscountRate(params);
  }
  doSave() {
    // @ts-ignore
    let params: BQuotationApplyDiscountRequest = {
      id: this.baseEntity.id!,
      matLines: this.tableData,
      outGoing: null,
      version: this.baseEntity.version!,
    };
    return QuotationApplyPriceApi.saveDiscountRate(params);
  }
  resizeHandler() {
    this.height = window.innerHeight - 570 + 'px';
  }
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler);
  }
}
